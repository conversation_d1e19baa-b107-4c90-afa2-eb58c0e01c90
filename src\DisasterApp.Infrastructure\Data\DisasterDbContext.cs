﻿using System;
using System.Collections.Generic;
using DisasterApp.Domain.Entities;
using Microsoft.EntityFrameworkCore;

namespace DisasterApp.Infrastructure.Data;

public partial class DisasterDbContext : DbContext
{
    public DisasterDbContext()
    {
    }

    public DisasterDbContext(DbContextOptions<DisasterDbContext> options)
        : base(options)
    {
    }

    public virtual DbSet<AssistanceProvided> AssistanceProvideds { get; set; }

    public virtual DbSet<Chat> Chats { get; set; }

    public virtual DbSet<DisasterCategory> DisasterCategories { get; set; }

    public virtual DbSet<DisasterEvent> DisasterEvents { get; set; }

    public virtual DbSet<DisasterReport> DisasterReports { get; set; }

    public virtual DbSet<DisasterType> DisasterTypes { get; set; }

    public virtual DbSet<Donation> Donations { get; set; }

    public virtual DbSet<ImpactDetail> ImpactDetails { get; set; }

    public virtual DbSet<ImpactType> ImpactTypes { get; set; }

    public virtual DbSet<Location> Locations { get; set; }

    public virtual DbSet<Organization> Organizations { get; set; }

    public virtual DbSet<Photo> Photos { get; set; }

    public virtual DbSet<RefreshToken> RefreshTokens { get; set; }

    public virtual DbSet<Role> Roles { get; set; }

    public virtual DbSet<SupportRequest> SupportRequests { get; set; }

    public virtual DbSet<SupportType> SupportTypes { get; set; }

    public virtual DbSet<User> Users { get; set; }

    protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
#warning To protect potentially sensitive information in your connection string, you should move it out of source code. You can avoid scaffolding the connection string by using the Name= syntax to read it from configuration - see https://go.microsoft.com/fwlink/?linkid=2131148. For more guidance on storing connection strings, see https://go.microsoft.com/fwlink/?LinkId=723263.
        => optionsBuilder.UseSqlServer("Server=.;Database=Disaster;User Id=sa;Password=********;TrustServerCertificate=true");

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        modelBuilder.Entity<AssistanceProvided>(entity =>
        {
            entity.HasKey(e => e.AssistanceId).HasName("PK__Assistan__13BD40CA61481D01");

            entity.ToTable("AssistanceProvided");

            entity.Property(e => e.AssistanceId)
                .HasDefaultValueSql("(newid())")
                .HasColumnName("assistance_id");
            entity.Property(e => e.CompletedAt).HasColumnName("completed_at");
            entity.Property(e => e.CreatedAt)
                .HasDefaultValueSql("(sysutcdatetime())")
                .HasColumnName("created_at");
            entity.Property(e => e.Notes).HasColumnName("notes");
            entity.Property(e => e.ProvidedAt)
                .HasDefaultValueSql("(sysutcdatetime())")
                .HasColumnName("provided_at");
            entity.Property(e => e.ProviderId).HasColumnName("provider_id");
            entity.Property(e => e.QuantityProvided)
                .HasDefaultValue(1)
                .HasColumnName("quantity_provided");
            entity.Property(e => e.RequestId).HasColumnName("request_id");
            entity.Property(e => e.Status)
                .HasDefaultValue((byte)1)
                .HasColumnName("status");
            entity.Property(e => e.UpdatedAt).HasColumnName("updated_at");

            entity.HasOne(d => d.Provider).WithMany(p => p.AssistanceProvideds)
                .HasForeignKey(d => d.ProviderId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_AssistanceProvided_Provider");

            entity.HasOne(d => d.Request).WithMany(p => p.AssistanceProvideds)
                .HasForeignKey(d => d.RequestId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_AssistanceProvided_Request");
        });

        modelBuilder.Entity<Chat>(entity =>
        {
            entity.HasKey(e => e.ChatId).HasName("PK__Chats__FD040B1769A39242");

            entity.Property(e => e.ChatId).HasColumnName("chat_id");
            entity.Property(e => e.AttachmentUrl)
                .HasMaxLength(512)
                .HasColumnName("attachment_url");
            entity.Property(e => e.IsRead)
                .HasDefaultValue(false)
                .HasColumnName("is_read");
            entity.Property(e => e.Message).HasColumnName("message");
            entity.Property(e => e.ReceiverId).HasColumnName("receiver_id");
            entity.Property(e => e.SenderId).HasColumnName("sender_id");
            entity.Property(e => e.SentAt)
                .HasDefaultValueSql("(sysutcdatetime())")
                .HasColumnName("sent_at");

            entity.HasOne(d => d.Receiver).WithMany(p => p.ChatReceivers)
                .HasForeignKey(d => d.ReceiverId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_Chat_Receiver");

            entity.HasOne(d => d.Sender).WithMany(p => p.ChatSenders)
                .HasForeignKey(d => d.SenderId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_Chat_Sender");
        });

        modelBuilder.Entity<DisasterCategory>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__Disaster__3214EC07E96F7950");

            entity.ToTable("DisasterCategory");

            entity.HasIndex(e => e.Name, "UQ__Disaster__737584F6D9DAA8ED").IsUnique();

            entity.Property(e => e.Id).ValueGeneratedNever();
            entity.Property(e => e.Name)
                .HasMaxLength(20)
                .IsUnicode(false);
        });

        modelBuilder.Entity<DisasterEvent>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__Disaster__3213E83F0C630599");

            entity.ToTable("DisasterEvent");

            entity.Property(e => e.Id)
                .HasDefaultValueSql("(newid())")
                .HasColumnName("id");
            entity.Property(e => e.DisasterTypeId).HasColumnName("disaster_type_id");
            entity.Property(e => e.Name)
                .HasMaxLength(100)
                .HasColumnName("name");

            entity.HasOne(d => d.DisasterType).WithMany(p => p.DisasterEvents)
                .HasForeignKey(d => d.DisasterTypeId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK__DisasterE__disas__41B8C09B");
        });

        modelBuilder.Entity<DisasterReport>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__Disaster__3213E83F1C33B9DA");

            entity.ToTable("DisasterReport");

            entity.Property(e => e.Id)
                .HasDefaultValueSql("(newid())")
                .HasColumnName("id");
            entity.Property(e => e.CreatedAt)
                .HasDefaultValueSql("(sysutcdatetime())")
                .HasColumnName("created_at");
            entity.Property(e => e.Description).HasColumnName("description");
            entity.Property(e => e.DisasterEventId).HasColumnName("disaster_event_id");
            entity.Property(e => e.EstimatedAffected).HasColumnName("estimated_affected");
            entity.Property(e => e.IsDeleted)
                .HasDefaultValue(false)
                .HasColumnName("is_deleted");
            entity.Property(e => e.Severity).HasColumnName("severity");
            entity.Property(e => e.Status)
                .HasDefaultValue((byte)1)
                .HasColumnName("status");
            entity.Property(e => e.Timestamp).HasColumnName("timestamp");
            entity.Property(e => e.Title)
                .HasMaxLength(200)
                .HasColumnName("title");
            entity.Property(e => e.UpdatedAt).HasColumnName("updated_at");
            entity.Property(e => e.UserId).HasColumnName("user_id");
            entity.Property(e => e.VerifiedAt).HasColumnName("verified_at");
            entity.Property(e => e.VerifiedBy).HasColumnName("verified_by");

            entity.HasOne(d => d.DisasterEvent).WithMany(p => p.DisasterReports)
                .HasForeignKey(d => d.DisasterEventId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_DisasterReport_DisasterEvent");

            entity.HasOne(d => d.User).WithMany(p => p.DisasterReportUsers)
                .HasForeignKey(d => d.UserId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_DisasterReport_User");

            entity.HasOne(d => d.VerifiedByNavigation).WithMany(p => p.DisasterReportVerifiedByNavigations)
                .HasForeignKey(d => d.VerifiedBy)
                .HasConstraintName("FK_DisasterReport_VerifiedBy");
        });

        modelBuilder.Entity<DisasterType>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__Disaster__3213E83F43004DCF");

            entity.ToTable("DisasterType");

            entity.HasIndex(e => e.Name, "UQ__Disaster__72E12F1B433397EB").IsUnique();

            entity.Property(e => e.Id).HasColumnName("id");
            entity.Property(e => e.CategoryId).HasColumnName("category_id");
            entity.Property(e => e.IconName)
                .HasMaxLength(50)
                .HasColumnName("icon_name");
            entity.Property(e => e.Name)
                .HasMaxLength(50)
                .HasColumnName("name");
            entity.Property(e => e.OrganizationId).HasColumnName("organization_id");

            entity.HasOne(d => d.Category).WithMany(p => p.DisasterTypes)
                .HasForeignKey(d => d.CategoryId)
                .HasConstraintName("FK_DisasterType_Category");

            entity.HasOne(d => d.Organization).WithMany(p => p.DisasterTypes)
                .HasForeignKey(d => d.OrganizationId)
                .HasConstraintName("FK_DisasterType_Organization");
        });

        modelBuilder.Entity<Donation>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__Donation__3213E83F4C3E4EA3");

            entity.Property(e => e.Id).HasColumnName("id");
            entity.Property(e => e.Amount)
                .HasColumnType("decimal(12, 2)")
                .HasColumnName("amount");
            entity.Property(e => e.Currency)
                .HasMaxLength(3)
                .IsUnicode(false)
                .HasDefaultValue("USD")
                .HasColumnName("currency");
            entity.Property(e => e.Description).HasColumnName("description");
            entity.Property(e => e.DistributedTo).HasColumnName("distributed_to");
            entity.Property(e => e.DonationType)
                .HasMaxLength(50)
                .HasColumnName("donation_type");
            entity.Property(e => e.DonorContact)
                .HasMaxLength(255)
                .HasColumnName("donor_contact");
            entity.Property(e => e.DonorName)
                .HasMaxLength(100)
                .HasColumnName("donor_name");
            entity.Property(e => e.ReceivedAt)
                .HasDefaultValueSql("(sysutcdatetime())")
                .HasColumnName("received_at");
            entity.Property(e => e.ReportId).HasColumnName("report_id");
            entity.Property(e => e.Status)
                .HasMaxLength(20)
                .IsUnicode(false)
                .HasDefaultValue("Pending")
                .HasColumnName("status");
            entity.Property(e => e.UserId).HasColumnName("user_id");
            entity.Property(e => e.VerifiedAt).HasColumnName("verified_at");
            entity.Property(e => e.VerifiedBy).HasColumnName("verified_by");

            entity.HasOne(d => d.Report).WithMany(p => p.Donations)
                .HasForeignKey(d => d.ReportId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_Donations_Report");

            entity.HasOne(d => d.User).WithMany(p => p.DonationUsers)
                .HasForeignKey(d => d.UserId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_Donations_User");

            entity.HasOne(d => d.VerifiedByNavigation).WithMany(p => p.DonationVerifiedByNavigations)
                .HasForeignKey(d => d.VerifiedBy)
                .HasConstraintName("FK_Donations_VerifiedBy");
        });

        modelBuilder.Entity<ImpactDetail>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__ImpactDe__3213E83F8E740B80");

            entity.ToTable("ImpactDetail");

            entity.Property(e => e.Id).HasColumnName("id");
            entity.Property(e => e.Description).HasColumnName("description");
            entity.Property(e => e.ImpactTypeId).HasColumnName("impact_type_id");
            entity.Property(e => e.IsResolved)
                .HasDefaultValue(false)
                .HasColumnName("is_resolved");
            entity.Property(e => e.ReportId).HasColumnName("report_id");
            entity.Property(e => e.ResolvedAt).HasColumnName("resolved_at");
            entity.Property(e => e.Severity).HasColumnName("severity");

            entity.HasOne(d => d.ImpactType).WithMany(p => p.ImpactDetails)
                .HasForeignKey(d => d.ImpactTypeId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_ImpactDetail_ImpactType");

            entity.HasOne(d => d.Report).WithMany(p => p.ImpactDetails)
                .HasForeignKey(d => d.ReportId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_ImpactDetail_Report");
        });

        modelBuilder.Entity<ImpactType>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__ImpactTy__3213E83F560AA2C1");

            entity.ToTable("ImpactType");

            entity.HasIndex(e => e.Name, "UQ__ImpactTy__72E12F1B4AA4C730").IsUnique();

            entity.Property(e => e.Id).HasColumnName("id");
            entity.Property(e => e.Description)
                .HasMaxLength(255)
                .HasColumnName("description");
            entity.Property(e => e.Name)
                .HasMaxLength(50)
                .HasColumnName("name");
        });

        modelBuilder.Entity<Location>(entity =>
        {
            entity.HasKey(e => e.LocationId).HasName("PK__Location__771831EA81520751");

            entity.ToTable("Location");

            entity.HasIndex(e => e.ReportId, "UQ__Location__779B7C59E4A3E040").IsUnique();

            entity.Property(e => e.LocationId)
                .HasDefaultValueSql("(newid())")
                .HasColumnName("location_id");
            entity.Property(e => e.Address)
                .HasMaxLength(255)
                .HasColumnName("address");
            entity.Property(e => e.CoordinatePrecision)
                .HasMaxLength(20)
                .IsUnicode(false)
                .HasColumnName("coordinate_precision");
            entity.Property(e => e.FormattedAddress)
                .HasMaxLength(512)
                .HasColumnName("formatted_address");
            entity.Property(e => e.GooglePlaceId)
                .HasMaxLength(100)
                .HasColumnName("google_place_id");
            entity.Property(e => e.Latitude)
                .HasColumnType("decimal(10, 8)")
                .HasColumnName("latitude");
            entity.Property(e => e.Longitude)
                .HasColumnType("decimal(11, 8)")
                .HasColumnName("longitude");
            entity.Property(e => e.ReportId).HasColumnName("report_id");

            entity.HasOne(d => d.Report).WithOne(p => p.Location)
                .HasForeignKey<Location>(d => d.ReportId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_Location_Report");
        });

        modelBuilder.Entity<Organization>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__Organiza__3213E83FE53B6B28");

            entity.HasIndex(e => e.Name, "UQ__Organiza__72E12F1B7113F934").IsUnique();

            entity.Property(e => e.Id).HasColumnName("id");
            entity.Property(e => e.ContactEmail)
                .HasMaxLength(255)
                .HasColumnName("contact_email");
            entity.Property(e => e.CreatedAt)
                .HasDefaultValueSql("(sysutcdatetime())")
                .HasColumnName("created_at");
            entity.Property(e => e.Description).HasColumnName("description");
            entity.Property(e => e.IsVerified)
                .HasDefaultValue(false)
                .HasColumnName("is_verified");
            entity.Property(e => e.LogoUrl)
                .HasMaxLength(512)
                .HasColumnName("logo_url");
            entity.Property(e => e.Name)
                .HasMaxLength(255)
                .HasColumnName("name");
            entity.Property(e => e.UserId).HasColumnName("user_id");
            entity.Property(e => e.WebsiteUrl)
                .HasMaxLength(512)
                .HasColumnName("website_url");

            entity.HasOne(d => d.User).WithMany(p => p.Organizations)
                .HasForeignKey(d => d.UserId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_Organizations_User");
        });

        modelBuilder.Entity<Photo>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__Photo__3213E83F676C6DC5");

            entity.ToTable("Photo");

            entity.Property(e => e.Id).HasColumnName("id");
            entity.Property(e => e.Caption)
                .HasMaxLength(255)
                .HasColumnName("caption");
            entity.Property(e => e.IsPrimary)
                .HasDefaultValue(false)
                .HasColumnName("is_primary");
            entity.Property(e => e.ReportId).HasColumnName("report_id");
            entity.Property(e => e.Type).HasColumnName("type");
            entity.Property(e => e.UploadedAt)
                .HasDefaultValueSql("(sysutcdatetime())")
                .HasColumnName("uploaded_at");
            entity.Property(e => e.Url)
                .HasMaxLength(512)
                .HasColumnName("url");

            entity.HasOne(d => d.Report).WithMany(p => p.Photos)
                .HasForeignKey(d => d.ReportId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_Photo_Report");
        });

        modelBuilder.Entity<RefreshToken>(entity =>
        {
            entity.HasKey(e => e.RefreshTokenId).HasName("PK__RefreshT__B0A1F7C71F7A268A");

            entity.ToTable("RefreshToken");

            entity.HasIndex(e => e.UserId, "IX_RefreshToken_user_id");

            entity.HasIndex(e => e.Token, "UQ__RefreshT__CA90DA7A24B1CB16").IsUnique();

            entity.Property(e => e.RefreshTokenId)
                .HasDefaultValueSql("(newid())")
                .HasColumnName("refresh_token_id");
            entity.Property(e => e.CreatedAt)
                .HasDefaultValueSql("(sysutcdatetime())")
                .HasColumnName("created_at");
            entity.Property(e => e.ExpiredAt).HasColumnName("expired_at");
            entity.Property(e => e.Token)
                .HasMaxLength(512)
                .HasColumnName("token");
            entity.Property(e => e.UserId).HasColumnName("user_id");

            entity.HasOne(d => d.User).WithMany(p => p.RefreshTokens)
                .HasForeignKey(d => d.UserId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_RefreshToken_User");
        });

        modelBuilder.Entity<Role>(entity =>
        {
            entity.HasKey(e => e.RoleId).HasName("PK__Role__760965CCCEA47220");

            entity.ToTable("Role");

            entity.HasIndex(e => e.Name, "UQ__Role__72E12F1B6DD5B5D7").IsUnique();

            entity.Property(e => e.RoleId)
                .HasDefaultValueSql("(newid())")
                .HasColumnName("role_id");
            entity.Property(e => e.Name)
                .HasMaxLength(50)
                .HasColumnName("name");
        });

        modelBuilder.Entity<SupportRequest>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__SupportR__3213E83F7F7A8BE5");

            entity.ToTable("SupportRequest");

            entity.Property(e => e.Id).HasColumnName("id");
            entity.Property(e => e.CreatedAt)
                .HasDefaultValueSql("(sysutcdatetime())")
                .HasColumnName("created_at");
            entity.Property(e => e.Deadline).HasColumnName("deadline");
            entity.Property(e => e.Description).HasColumnName("description");
            entity.Property(e => e.Priority).HasColumnName("priority");
            entity.Property(e => e.QuantityNeeded).HasColumnName("quantity_needed");
            entity.Property(e => e.QuantityReceived)
                .HasDefaultValue(0)
                .HasColumnName("quantity_received");
            entity.Property(e => e.ReportId).HasColumnName("report_id");
            entity.Property(e => e.Status)
                .HasDefaultValue((byte)1)
                .HasColumnName("status");
            entity.Property(e => e.SupportTypeId).HasColumnName("support_type_id");
            entity.Property(e => e.UpdatedAt).HasColumnName("updated_at");
            entity.Property(e => e.Urgency).HasColumnName("urgency");
            entity.Property(e => e.UserId).HasColumnName("user_id");

            entity.HasOne(d => d.Report).WithMany(p => p.SupportRequests)
                .HasForeignKey(d => d.ReportId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_SupportRequest_Report");

            entity.HasOne(d => d.SupportType).WithMany(p => p.SupportRequests)
                .HasForeignKey(d => d.SupportTypeId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_SupportRequest_SupportType");

            entity.HasOne(d => d.User).WithMany(p => p.SupportRequests)
                .HasForeignKey(d => d.UserId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_SupportRequest_User");
        });

        modelBuilder.Entity<SupportType>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__SupportT__3213E83FC38913DF");

            entity.ToTable("SupportType");

            entity.HasIndex(e => e.Name, "UQ__SupportT__72E12F1B1361FEE0").IsUnique();

            entity.Property(e => e.Id).HasColumnName("id");
            entity.Property(e => e.Description)
                .HasMaxLength(255)
                .HasColumnName("description");
            entity.Property(e => e.Name)
                .HasMaxLength(50)
                .HasColumnName("name");
        });

        modelBuilder.Entity<User>(entity =>
        {
            entity.HasKey(e => e.UserId).HasName("PK__User__B9BE370FF42BE9EA");

            entity.ToTable("User");

            entity.HasIndex(e => e.Email, "IX_User_Email");

            entity.HasIndex(e => new { e.AuthProvider, e.AuthId }, "UQ_User_AuthProviderId").IsUnique();

            entity.HasIndex(e => e.Email, "UQ__User__AB6E61647E5028D0").IsUnique();

            entity.Property(e => e.UserId)
                .HasDefaultValueSql("(newid())")
                .HasColumnName("user_id");
            entity.Property(e => e.AuthId)
                .HasMaxLength(255)
                .HasColumnName("auth_id");
            entity.Property(e => e.AuthProvider)
                .HasMaxLength(20)
                .IsUnicode(false)
                .HasColumnName("auth_provider");
            entity.Property(e => e.CreatedAt)
                .HasDefaultValueSql("(sysutcdatetime())")
                .HasColumnName("created_at");
            entity.Property(e => e.Email)
                .HasMaxLength(255)
                .HasColumnName("email");
            entity.Property(e => e.IsBlacklisted)
                .HasDefaultValue(false)
                .HasColumnName("is_blacklisted");
            entity.Property(e => e.Name)
                .HasMaxLength(100)
                .HasColumnName("name");
            entity.Property(e => e.PhotoUrl)
                .HasMaxLength(512)
                .HasColumnName("photo_url");

            entity.HasMany(d => d.Roles).WithMany(p => p.Users)
                .UsingEntity<Dictionary<string, object>>(
                    "UserRole",
                    r => r.HasOne<Role>().WithMany()
                        .HasForeignKey("RoleId")
                        .OnDelete(DeleteBehavior.ClientSetNull)
                        .HasConstraintName("FK_UserRole_Role"),
                    l => l.HasOne<User>().WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.ClientSetNull)
                        .HasConstraintName("FK_UserRole_User"),
                    j =>
                    {
                        j.HasKey("UserId", "RoleId");
                        j.ToTable("UserRole");
                        j.HasIndex(new[] { "RoleId" }, "IX_UserRole_role_id");
                        j.IndexerProperty<Guid>("UserId").HasColumnName("user_id");
                        j.IndexerProperty<Guid>("RoleId").HasColumnName("role_id");
                    });
        });

        OnModelCreatingPartial(modelBuilder);
    }

    partial void OnModelCreatingPartial(ModelBuilder modelBuilder);
}
